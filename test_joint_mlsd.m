% Joint MLSD + Timing Recovery 테스트 스크립트
clear; clc; close all;

fprintf('=== Joint MLSD + Timing Recovery 테스트 ===\n');

try
    % 1. 테스트 데이터 생성
    fprintf('1. 테스트 데이터 생성 중...\n');
    if ~exist('DumpData/DUMPDATA_250525_ch2.bin', 'file')
        fprintf('   테스트 데이터 파일이 없습니다. 생성 중...\n');
        run('create_test_data.m');
    end
    
    % 2. 메인 스크립트 실행 전 기본 검증
    fprintf('2. 기본 구문 검증 중...\n');
    
    % 필수 상수들 정의 (메인 스크립트에서 가져옴)
    BT = 0.5;
    OSR = 5;
    USE_CHx_RAW_DATA = 1;
    ENABLE_GMSK_RX_FLT = 1;
    ENABLE_NOTCH_FLT = 0;
    ENABLE_ADAPT_DC_OFFSET = 1;
    ADC_SUB_DC_OFFSET = 32768;
    ADC_MAX_VALUE = 32768;
    
    % Joint MLSD 설정
    ENABLE_JOINT_MLSD_TIMING = 1;
    ENABLE_VITERBI_MLSD = 1;
    VITERBI_WINDOW_SIZE = 16;
    MLSD_TIMING_WINDOW = 32;
    MLSD_SYMBOL_BUFFER_SIZE = 64;
    
    fprintf('   기본 설정 완료\n');
    
    % 3. 데이터 로드 테스트
    fprintf('3. 데이터 로드 테스트 중...\n');
    filename = './DumpData/DUMPDATA_250525_ch2.bin';
    fid = fopen(filename, 'rb');
    if fid == -1
        error('파일을 열 수 없습니다: %s', filename);
    end
    
    test_data = fread(fid, 1000, 'uint16');  % 처음 1000 샘플만 테스트
    fclose(fid);
    
    if isempty(test_data)
        error('데이터가 비어있습니다');
    end
    
    fprintf('   데이터 로드 성공: %d 샘플\n', length(test_data));
    
    % 4. 데이터 전처리 테스트
    fprintf('4. 데이터 전처리 테스트 중...\n');
    normalized_data = (double(test_data) - ADC_SUB_DC_OFFSET) / ADC_MAX_VALUE;
    fprintf('   정규화 완료: 범위 [%.3f, %.3f]\n', min(normalized_data), max(normalized_data));
    
    % 5. 기본 함수 테스트
    fprintf('5. 기본 함수 테스트 중...\n');
    
    % NRZI 디코딩 테스트
    test_sample = normalized_data(100);
    test_ref = mean(normalized_data(1:50));
    
    if test_sample > test_ref
        nrzi_result = 1;
    else
        nrzi_result = 0;
    end
    
    fprintf('   NRZI 디코딩 테스트: 샘플=%.3f, 기준=%.3f, 결과=%d\n', ...
            test_sample, test_ref, nrzi_result);
    
    % 6. 간단한 신호 처리 테스트
    fprintf('6. 신호 처리 테스트 중...\n');
    
    % 간단한 상관관계 계산
    preamble_pattern = [1, 0, 1, 0, 1, 0, 1, 0];  % 간단한 패턴
    if length(normalized_data) >= length(preamble_pattern)
        correlation = sum(normalized_data(1:length(preamble_pattern)) .* preamble_pattern');
        fprintf('   상관관계 계산 성공: %.3f\n', correlation);
    end
    
    % 7. 메모리 할당 테스트
    fprintf('7. 메모리 할당 테스트 중...\n');
    
    % MLSD 버퍼 할당
    mlsd_buffer = zeros(1, MLSD_TIMING_WINDOW);
    symbol_buffer = zeros(1, MLSD_SYMBOL_BUFFER_SIZE);
    viterbi_buffer = zeros(1, VITERBI_WINDOW_SIZE);
    
    fprintf('   메모리 할당 성공\n');
    
    % 8. 기본 통계
    fprintf('8. 기본 통계:\n');
    fprintf('   - 데이터 길이: %d\n', length(test_data));
    fprintf('   - 평균값: %.3f\n', mean(normalized_data));
    fprintf('   - 표준편차: %.3f\n', std(normalized_data));
    fprintf('   - 최소값: %.3f\n', min(normalized_data));
    fprintf('   - 최대값: %.3f\n', max(normalized_data));
    
    fprintf('\n=== 모든 기본 테스트 통과! ===\n');
    fprintf('이제 메인 스크립트를 실행할 수 있습니다.\n');
    
catch ME
    fprintf('\n!!! 오류 발생 !!!\n');
    fprintf('오류 메시지: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('파일: %s\n', ME.stack(1).file);
        fprintf('함수: %s\n', ME.stack(1).name);
        fprintf('라인: %d\n', ME.stack(1).line);
    end
    
    fprintf('\n디버깅 정보:\n');
    fprintf('- 현재 작업 디렉토리: %s\n', pwd);
    fprintf('- DumpData 폴더 존재: %s\n', mat2str(exist('DumpData', 'dir')));
    if exist('DumpData', 'dir')
        files = dir('DumpData/*.bin');
        fprintf('- 데이터 파일 개수: %d\n', length(files));
        for i = 1:length(files)
            fprintf('  - %s\n', files(i).name);
        end
    end
end
