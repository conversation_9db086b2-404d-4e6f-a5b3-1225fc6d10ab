% 테스트용 더미 데이터 생성
clear; clc;

fprintf('테스트용 더미 데이터 생성 중...\n');

% DumpData 폴더 생성
if ~exist('DumpData', 'dir')
    mkdir('DumpData');
end

% 테스트용 AIS 신호 생성 (간단한 GMSK 신호)
fs = 9600 * 5;  % 샘플링 주파수 (OSR = 5)
duration = 10;   % 10초
t = 0:1/fs:duration-1/fs;

% 기본 캐리어 + 노이즈
carrier_freq = 1000;  % 1kHz 캐리어
signal = sin(2*pi*carrier_freq*t);

% GMSK 변조 시뮬레이션 (간단한 버전)
bit_rate = 9600;
samples_per_bit = fs / bit_rate;

% 랜덤 비트 생성
num_bits = floor(length(t) / samples_per_bit);
bits = randi([0, 1], 1, num_bits);

% 간단한 FSK 변조
modulated_signal = zeros(size(t));
for i = 1:num_bits
    start_idx = round((i-1) * samples_per_bit) + 1;
    end_idx = min(round(i * samples_per_bit), length(t));
    
    if bits(i) == 1
        freq = carrier_freq + 500;  % +500Hz for '1'
    else
        freq = carrier_freq - 500;  % -500Hz for '0'
    end
    
    modulated_signal(start_idx:end_idx) = sin(2*pi*freq*t(start_idx:end_idx));
end

% 노이즈 추가
noise_level = 0.1;
noisy_signal = modulated_signal + noise_level * randn(size(modulated_signal));

% ADC 시뮬레이션 (16비트, 0-65535 범위)
adc_offset = 32768;  % DC 오프셋
adc_gain = 10000;    % 게인
adc_data = round(adc_offset + adc_gain * noisy_signal);
adc_data = max(0, min(65535, adc_data));  % 클리핑

% uint16으로 변환
adc_data = uint16(adc_data);

% 파일 저장
filename1 = 'DumpData/DUMPDATA_250525_ch1.bin';
filename2 = 'DumpData/DUMPDATA_250525_ch2.bin';

fid1 = fopen(filename1, 'wb');
fwrite(fid1, adc_data, 'uint16');
fclose(fid1);

fid2 = fopen(filename2, 'wb');
fwrite(fid2, adc_data, 'uint16');
fclose(fid2);

fprintf('테스트 데이터 생성 완료:\n');
fprintf('- %s (%d 샘플)\n', filename1, length(adc_data));
fprintf('- %s (%d 샘플)\n', filename2, length(adc_data));
fprintf('- 샘플링 주파수: %d Hz\n', fs);
fprintf('- 지속 시간: %.1f 초\n', duration);
