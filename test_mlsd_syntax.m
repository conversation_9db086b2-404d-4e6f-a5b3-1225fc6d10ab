% 간단한 구문 테스트 스크립트
clear all; close all; clc;

try
    fprintf('MATLAB 구문 검사 시작...\n');

    % 기본 변수들 설정
    ENABLE_JOINT_MLSD_TIMING = 1;
    ENABLE_VITERBI_MLSD = 1;
    VITERBI_WINDOW_SIZE = 16;
    VITERBI_CONFIDENCE_THRESHOLD = 0.3;
    MLSD_TIMING_WINDOW = 32;
    MLSD_SYMBOL_BUFFER_SIZE = 64;
    
    % 테스트용 더미 데이터
    G_pFilteredData = randn(1, 1000);
    G_pSrcDataCh1 = randn(1, 1000);
    ENABLE_GMSK_RX_FLT = 1;
    
    % MLSD 변수들 초기화
    G_MLSDTimingBuffer = zeros(1, MLSD_TIMING_WINDOW);
    G_MLSDTimingIndex = 1;
    G_MLSDSymbolBuffer = zeros(1, MLSD_SYMBOL_BUFFER_SIZE);
    G_MLSDSymbolIndex = 1;
    G_MLSDTimingOffset = 0.0;
    G_MLSDTimingPhase = 0.0;
    G_MLSDSampleCounter = 0;
    G_MLSDSymbolCounter = 0;
    G_MLSDTimingLocked = false;
    G_MLSDTimingQuality = 0.0;
    
    % Viterbi 통계 초기화
    G_ViterbiStats = struct();
    G_ViterbiStats.total_decisions = 0;
    G_ViterbiStats.viterbi_decisions = 0;
    G_ViterbiStats.confidence_sum = 0;
    
    fprintf('변수 초기화 완료\n');
    
    % 함수 테스트
    test_signal = 0.5;
    test_idx = 100;
    
    fprintf('Joint MLSD 함수 테스트 중...\n');
    [bit_result, timing_result, conf_result] = joint_mlsd_timing_recovery(test_signal, test_idx);
    
    fprintf('테스트 완료!\n');
    fprintf('비트 결정: %d\n', bit_result);
    fprintf('타이밍 결정: %d\n', timing_result);
    fprintf('신뢰도: %.3f\n', conf_result);
    
catch ME
    fprintf('오류 발생: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('파일: %s, 라인: %d\n', ME.stack(1).name, ME.stack(1).line);
    end
    rethrow(ME);
end
