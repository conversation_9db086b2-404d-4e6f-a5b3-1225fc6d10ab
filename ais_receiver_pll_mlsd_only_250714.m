clear;

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
USE_CHx_RAW_DATA        = 1;                % 0: Ch1, 1: Ch2 Raw data
ENABLE_NOTCH_FLT        = 1;                % 0: Disable, 1: Enable Notch Filter
ENABLE_GMSK_RX_FLT      = 1;                % 0: Disable, 1: New GMSK Filter
ENABLE_ADAPT_DC_OFFSET  = 1;                % 0: Disable, 1: Adaptive DC Offset
ENABLE_ADC_LIMIT        = 2;                % 0: Disable, 1: Min/Max Enable, 2: Max only Enable

% ★ Joint MLSD + Timing Recovery 설정 (완전 MLSD 구현)
ENABLE_VITERBI_MLSD     = 1;                % 0: 기존 방식, 1: Joint MLSD + Timing
ENABLE_JOINT_MLSD_TIMING = 1;               % 0: 기존 PLL, 1: MLSD 기반 타이밍 복구
VITERBI_WINDOW_SIZE     = 16;               % Viterbi 윈도우 크기 (증가)
VITERBI_CONFIDENCE_THRESHOLD = 0.3;         % 신뢰도 임계값
MLSD_TIMING_WINDOW      = 32;               % MLSD 타이밍 복구 윈도우
MLSD_SYMBOL_BUFFER_SIZE = 64;               % 심볼 버퍼 크기
%-------------------------------------------------------------------------
ENABLE_DEBUG            = 0;
if (ENABLE_DEBUG == 1)
    ENABLE_PLOT1        = 1;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 1;                % 0: Disable, 1: Start detection
    ENABLE_PLOT3        = 1;                % 0: Disable, 1: Received Data packet with CRC
else
    ENABLE_PLOT1        = 0;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 0;                % 0: Disable, 1: Start detection
    ENABLE_PLOT3        = 0;                % 0: Disable, 1: Received Data packet with CRC
end

ENABLE_DEBUG_ERROR      = 0;
if (ENABLE_DEBUG_ERROR == 1)
    ENABLE_PLOT96       = 1;                % 0: Disable, 1: ADC Max/Min Error
    ENABLE_PLOT97       = 1;                % 0: Disable, 1: Start Bit Error
    ENABLE_PLOT98       = 1;                % 0: Disable, 1: Stuffing Bit Error
    ENABLE_PLOT99       = 1;                % 0: Disable, 1: CRC Error
else
    ENABLE_PLOT96       = 0;                % 0: Disable, 1: ADC Max/Min Error
    ENABLE_PLOT97       = 0;                % 0: Disable, 1: Start Bit Error
    ENABLE_PLOT98       = 0;                % 0: Disable, 1: Stuffing Bit Error
    ENABLE_PLOT99       = 0;                % 0: Disable, 1: CRC Error
end

%-------------------------------------------------------------------------
BIT_RATE                = 9600;             % Bit rate
OSR                     = 5;                % Over sampling rate
BT                      = 0.4;              % Transmit BT product
RX_BT                   = 0.5;              % Receive BT product
LEN_PSF                 = 8 * OSR;          % Pulse shaping filter length
H_NORM                  = 3;                % 1: normalized by h_max, 2: normalized by norm(h), 3: no normalized

ADC_RES                 = 12;               % 12bit resolution
ADC_MAX_VALUE           = 4095;
ADC_MAX_ERROR_CNT       = 30;

%-------------------------------------------------------------------------
MAX_SYNC_CORRVAL        = .750;
MAX_SYNC_COUNT          = 25;
DC_AVG_OFFSET           = (OSR*5);
DC_AVG_COUNT            = 55;
DC_GAP                  = 0.000;
START_DETECT_OFFSET     = (OSR*8);
SYNC_DETECT_OFFSET      = (OSR*6)+2;
ADC_SUB_DC_OFFSET       = 1450;

%-------------------------------------------------------------------------
 % Legacy modem defines
 NOTCH_FLT_A            = [+1.999986841577810, -0.999986910116283];
 NOTCH_FLT_B            = [+0.999993455058141, -1.999986841577810, +0.999993455058141];
 
 DC_MIN_LEVEL           = (   0 / 3300);    % 0.05V
 DC_MID_LEVEL           = (1000 / 3300);    % 1.00V
 DC_MAX_LEVEL           = (1850 / 3300);    % 1.85V

 RX_PLL_FULL            = 2400;
 RX_PLL_HALF            = (RX_PLL_FULL / 2);
 RX_PLL_INCR            = (RX_PLL_FULL / OSR);
 RX_PLL_STEP            = (RX_PLL_INCR / 3);

 RX_GMSK_BT_0_4_FIR_N   = 17;
 RX_GMSK_BT_0_5_FIR_N   = 13;
 RX_GMSK_TO_INT_FACTOR  = 16;

 RX_GMSK_MAX_DATA_VALUE = (BIT_RATE*OSR*RX_GMSK_TO_INT_FACTOR);

 RX_MDM_STATUS_PREAMBLE = 0;
 RX_MDM_STATUS_START    = 1;
 RX_MDM_STATUS_PRELOAD  = 2;
 RX_MDM_STATUS_DATA     = 3;

 RX_DOT_MAX_CNT_SIZE    = 7;
 RX_DOT_MAX_CNT_MASK    = 0x7f;
 RX_DOT_START_P_MASK    = 0x05;
 RX_DOT_DETCT_P_MASK    = 0x55;
 RX_DOT_MAX_CNT_LAST    = RX_DOT_MAX_CNT_SIZE;

 RX_PRE_MAX_CNT_SIZE    = 12;
 RX_PRE_MAX_BUF_SIZE    = (RX_PRE_MAX_CNT_SIZE * OSR);

 G_vNotchDataX = zeros(1, 3);

 G_vReverDataTableX     = [ ...
  %   0    1    2    3   4    5    6    7   8    9    a    b   c    d    e    f 
      0,  -1,  -1,  -1, 32,  -1,  -1,  -1, 16,  -1,  -1,  -1, 48,  -1,  -1,  -1, ... % 00--0f
      8,  -1,  -1,  -1, 40,  -1,  -1,  -1, 24,  -1,  -1,  -1, 56,  -1,  -1,  -1, ... % 10--1f
      4,  -1,  -1,  -1, 36,  -1,  -1,  -1, 20,  -1,  -1,  -1, 52,  -1,  -1,  -1, ... % 20--2f
     12,  -1,  -1,  -1, 44,  -1,  -1,  -1, 28,  -1,  -1,  -1, 60,  -1,  -1,  -1, ... % 30--3f
      2,  -1,  -1,  -1, 34,  -1,  -1,  -1, 18,  -1,  -1,  -1, 50,  -1,  -1,  -1, ... % 40--4f
     10,  -1,  -1,  -1, 42,  -1,  -1,  -1, 26,  -1,  -1,  -1, 58,  -1,  -1,  -1, ... % 50--5f
      6,  -1,  -1,  -1, 38,  -1,  -1,  -1, 22,  -1,  -1,  -1, 54,  -1,  -1,  -1, ... % 60--6f
     14,  -1,  -1,  -1, 46,  -1,  -1,  -1, 30,  -1,  -1,  -1, 62,  -1,  -1,  -1, ... % 70--7f
      1,  -1,  -1,  -1, 33,  -1,  -1,  -1, 17,  -1,  -1,  -1, 49,  -1,  -1,  -1, ... % 80--8f
      9,  -1,  -1,  -1, 41,  -1,  -1,  -1, 25,  -1,  -1,  -1, 57,  -1,  -1,  -1, ... % 90--9f
      5,  -1,  -1,  -1, 37,  -1,  -1,  -1, 21,  -1,  -1,  -1, 53,  -1,  -1,  -1, ... % a0--af
     13,  -1,  -1,  -1, 45,  -1,  -1,  -1, 29,  -1,  -1,  -1, 61,  -1,  -1,  -1, ... % b0--bf
      3,  -1,  -1,  -1, 35,  -1,  -1,  -1, 19,  -1,  -1,  -1, 51,  -1,  -1,  -1, ... % c0--cf
     11,  -1,  -1,  -1, 43,  -1,  -1,  -1, 27,  -1,  -1,  -1, 59,  -1,  -1,  -1, ... % d0--df
      7,  -1,  -1,  -1, 39,  -1,  -1,  -1, 23,  -1,  -1,  -1, 55,  -1,  -1,  -1, ... % e0--ef
     15,  -1,  -1,  -1, 47,  -1,  -1,  -1, 31,  -1,  -1,  -1, 63,  -1,  -1,  -1];    % f0--ff

 G_vMaxBitSize          = [ ...
     1064,  168,  168,  168,  168,  424, 1008,  168, 1008,  168,   72,  168, 1008,  168, 1008,  160, ...
      144,  816,  168,  312,  160,  360,  168,  160,  168,  168, 1064,   96, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064];

%-------------------------------------------------------------------------
dot_pattern             = repmat([1, 1, 0, 0], 1, 6);       % Dot pattern
preamble                = dot_pattern';                     % Preamble (dot pattern only)
preamble_os             = repelem(preamble, OSR);           % Over sampled preamble
LEN_DOT_PATTERN         = length(dot_pattern);              % Length of dot pattern
LEN_PREAMBLE            = length(preamble);                 % Length of preamble
LEN_PREAMBLE_OS         = LEN_PREAMBLE*OSR;                 % Length of over sampled preamble
%-------------------------------------------------------------------------

%-------------------------------------------------------------------------
% functions
%-------------------------------------------------------------------------
% Parameters:
% BT: 대역폭-시간 곱
% OSR: 오버샘플링 비율
% LENGTH: 임펄스 응답의 길이
% NORM: Normalize method
function [h, t] = gmsk_impulse_response(BT, OSR, LENGTH, NORM)
% h: impulse response
% t: time index
    t = ((-LENGTH / 2):(LENGTH / 2)) / OSR;
    h = 0.5 * (erf(pi * BT * sqrt(2 / log(2)) * (t + 0.5)) ...
             - erf(pi * BT * sqrt(2 / log(2)) * (t - 0.5)));

    if (NORM == 1)
        h = h / max(h);
    elseif (NORM == 2)
        h = h / norm(h);
    end
end

% Notch Filter 적용
function y = apply_notch_filter(x, notch_state, NOTCH_FLT_A, NOTCH_FLT_B)
    rY = notch_state(1) + NOTCH_FLT_B(1) * x;
    notch_state(1) = (NOTCH_FLT_B(2) * x) + (NOTCH_FLT_A(1) * rY) + notch_state(2);
    notch_state(2) = (NOTCH_FLT_B(3) * x) + (NOTCH_FLT_A(2) * rY) + notch_state(3);
    y = rY;
end

%------------------------------------------------------------------------
% GMSK Filter 적용
function y = apply_gmsk_filter(x_buff, impulse_response)
    conv_data = conv(x_buff, impulse_response);
    y = conv_data(length(impulse_response));
end

%------------------------------------------------------------------------
% Adaptive DC Offset 보정 (반전 구간에서만)
function G_wRxReferValue = update_dc_offset_on_invert(G_wRxReferValue, sample, alpha_dc)
    G_wRxReferValue = alpha_dc * G_wRxReferValue + (1-alpha_dc) * sample;
end

%------------------------------------------------------------------------
% NRZI 해석
function bit = nrzi_decode(sample, refer)
    if sample > refer
        bit = 1;
    else
        bit = 0;
    end
end

%------------------------------------------------------------------------
% CRC 계산 (AIS 표준)
function crc = update_crc(crc, new_bit)
    if bitand(bitxor(crc, new_bit), 0x0001)
        crc = bitxor(bitshift(crc, -1), 0x8408);
    else
        crc = bitshift(crc, -1);
    end
end


%-------------------------------------------------------------------------
% impulse response of gmsk filter
%-------------------------------------------------------------------------
%SPAN = 3; SPS = 4;
%impulse_response_of_gmsk            = gmsk_impulse_response(BT, OSR, SPAN*SPS, 1);
SPAN = 3; SPS = 4;
impulse_response_of_gmsk            = gaussdesign(BT, SPAN, SPS);
impulse_response_of_gmsk_twice      = conv (impulse_response_of_gmsk, impulse_response_of_gmsk);
RX_GMSK_BT_0_5_FIR_N                = length(impulse_response_of_gmsk);

preamble_zero_padded                = upsample (preamble, OSR);
preamble_filtered_by_gmsk           = conv (preamble_zero_padded, impulse_response_of_gmsk);
preamble_filtered_by_gmsk_twice     = conv (preamble_zero_padded, impulse_response_of_gmsk_twice);

if (ENABLE_PLOT1 == 1)
    h_fig10 = figure(10);
    h_fig10.Name = 'gmsk and preamble';
    subplot(3,1,1); plot(impulse_response_of_gmsk, '-o'); grid; title('impulse\_response (gmsk) (o)');
    subplot(3,1,2); plot(preamble, '-o'); grid; title('preamble');
    subplot(3,1,3); plot(preamble_filtered_by_gmsk, '-o');
end

%-------------------------------------------------------------------------
% Variables
%-------------------------------------------------------------------------
G_vRxRawDataBuff        = zeros(1, RX_GMSK_BT_0_5_FIR_N);
G_xPreData              = struct('nPntX', uint8(0), ...
                                'dSumX', double(0), ...
                                'dCntX', uint16(0), ...
                                'wAvrX', double(DC_MID_LEVEL), ...
                                'vData', zeros(1,RX_PRE_MAX_BUF_SIZE));
G_xDotData              = struct('wDotPattern', uint16(0), ...
                                'wDotChanged', uint8(0), ...
                                'wDotCountX', uint8(0));
G_wRxShiftReg           = 0;
G_dSwRxPllCntrX         = 0;
G_dSwRxPllSampP         = 0;
G_dSwRxPllSampC         = 0;
G_wRxCurrBitD           = 0;
G_wRxPrevBitD           = 0;
G_wCrcRegData           = 0;
G_wRxBitCount           = 0;

G_wRxAfAdcData          = 0;
G_wRxNrziCntr           = 0;
G_wRxNrziCurr           = 0;
G_wRxNrziPrev           = 0;
G_wRxNrziTemp           = 0;
G_wRxReferValue         = DC_MID_LEVEL;
G_wRxRunStatus          = RX_MDM_STATUS_PREAMBLE;
G_dSwRxPllValue         = 0;
G_dRxAdcErrCnt          = 0;

G_wNewBitData           = 0;
G_bRxByteData           = 0;

G_PreStart              = 0;
G_PreOffset             = 300;
G_dSyncDetCnt           = 0;
G_dAdcErrCnt            = 0;
G_dStartErrCnt          = 0;
G_dPloadErrCnt          = 0;
G_dStuffErrCnt          = 0;
G_dCrcErrCnt            = 0;
G_dRcvPktCnt            = 0;

G_dRxAfAdcSumVal        = 0;
G_dRxAfAdcCntVal        = 0;
G_dMaxSyncCorrel        = 0;
G_dMaxSyncCnt           = 0;
G_dSyncSymbolIndex      = 0;
G_dStartSymbolIndex     = 0;

G_BitDataArray = zeros(1, 500);

% 개선된 성능을 위한 추가 변수들
G_CorrelHistory         = zeros(1, 100);    % 상관관계 히스토리
G_AdaptiveThreshold     = MAX_SYNC_CORRVAL; % 적응형 임계값
G_NoiseFloor            = 0.1;              % 노이즈 플로어
G_SignalQuality         = 0;                % 신호 품질 지표
G_HysteresisValue       = 0.02;             % NRZI 히스테리시스 값

% ★ Joint MLSD + Timing Recovery 관련 변수 (추가)
G_ViterbiBuffer         = zeros(1, VITERBI_WINDOW_SIZE);
G_ViterbiIndex          = 1;
G_ViterbiStats          = struct();
G_ViterbiStats.total_decisions = 0;
G_ViterbiStats.viterbi_decisions = 0;
G_ViterbiStats.confidence_sum = 0;

% MLSD 타이밍 복구 변수들
G_MLSDTimingBuffer      = zeros(1, MLSD_TIMING_WINDOW);
G_MLSDTimingIndex       = 1;
G_MLSDSymbolBuffer      = zeros(1, MLSD_SYMBOL_BUFFER_SIZE);
G_MLSDSymbolIndex       = 1;
G_MLSDTimingOffset      = 0.0;              % 타이밍 오프셋 추정값
G_MLSDTimingPhase       = 0.0;              % 타이밍 위상
G_MLSDSampleCounter     = 0;                % 샘플 카운터
G_MLSDSymbolCounter     = 0;                % 심볼 카운터
G_MLSDTimingLocked      = false;            % 타이밍 락 상태
G_MLSDTimingQuality     = 0.0;              % 타이밍 품질 지표



%-------------------------------------------------------------------------
% Raw data input
%-------------------------------------------------------------------------
if (USE_CHx_RAW_DATA == 0)
    filename = './DumpData/DUMPDATA_250525_ch1.bin';
else
    filename = './DumpData/DUMPDATA_250525_ch2.bin';
end

% 파일 존재 여부 확인
if ~exist(filename, 'file')
    error('데이터 파일을 찾을 수 없습니다: %s', filename);
end

G_hDumpFile = fopen(filename);
if G_hDumpFile == -1
    error('파일을 열 수 없습니다: %s', filename);
end

G_pSrcDataCh1 = fread(G_hDumpFile, 'uint16');
fclose(G_hDumpFile);

if isempty(G_pSrcDataCh1)
    error('데이터 파일이 비어있습니다: %s', filename);
end

G_pSrcDataCh1 = (G_pSrcDataCh1-ADC_SUB_DC_OFFSET) / ADC_MAX_VALUE;

% 성능 향상을 위한 배열 사전 할당
data_length = length(G_pSrcDataCh1);
G_pFilteredData = zeros(1, data_length);
CorrelPreamble = zeros(1, data_length);
AdativeDcOffset = zeros(1, data_length);
BitArray = zeros(1, data_length);
G_dCRCErrSymIdx = zeros(1, 1000);  % CRC 오류 인덱스 배열 사전 할당

for nCurSymbolIdx = 1:length(G_pSrcDataCh1)
    % Notch Filter
    if (ENABLE_NOTCH_FLT == 1)
        G_wRxAfAdcData = apply_notch_filter(G_pSrcDataCh1(nCurSymbolIdx), G_vNotchDataX, NOTCH_FLT_A, NOTCH_FLT_B);
    else
        G_wRxAfAdcData = G_pSrcDataCh1(nCurSymbolIdx);
    end

    % 안전한 버퍼 업데이트
    if length(G_vRxRawDataBuff) >= RX_GMSK_BT_0_5_FIR_N
        G_vRxRawDataBuff(1:end-1) = G_vRxRawDataBuff(2:end);
        G_vRxRawDataBuff(end) = G_wRxAfAdcData;
    else
        G_vRxRawDataBuff(end+1) = G_wRxAfAdcData;
    end

    % GMSK Filter
    G_pFilteredData(nCurSymbolIdx) = apply_gmsk_filter(G_vRxRawDataBuff, impulse_response_of_gmsk);

    G_vGmskPreamble = preamble_filtered_by_gmsk;
    % Correlation Preamble
    if (G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        if (nCurSymbolIdx <= length(G_vGmskPreamble))
            CorrelPreamble(nCurSymbolIdx) = 0;
        else
            % Preamble correlation detection은 GMSK Filter를 통과한 데이터를 사용하지 않는다.
            %if (ENABLE_GMSK_RX_FLT > 0)
            %    tmp_100 = G_pFilteredData(nCurSymbolIdx - length(G_vGmskPreamble) + 1 : nCurSymbolIdx);
            %    tmp_100 = tmp_100/norm(tmp_100);
            %    CorrelPreamble(nCurSymbolIdx) = (tmp_100) * G_vGmskPreamble / norm(G_vGmskPreamble);
            %else
                tmp_100 = G_pSrcDataCh1(nCurSymbolIdx - length(G_vGmskPreamble) + 1 : nCurSymbolIdx);
                tmp_100 = tmp_100/norm(tmp_100);
                CorrelPreamble(nCurSymbolIdx) = (tmp_100)' * G_vGmskPreamble / norm(G_vGmskPreamble);
            %end

            % 개선된 프리앰블 검출 (적응형 임계값 사용)
            % 상관관계 히스토리 업데이트
            G_CorrelHistory = [G_CorrelHistory(2:end), CorrelPreamble(nCurSymbolIdx)];

            % 신호 품질 평가
            recent_correl = G_CorrelHistory(max(1, end-10):end);
            G_SignalQuality = mean(recent_correl) / (std(recent_correl) + 0.01);

            if (CorrelPreamble(nCurSymbolIdx) > MAX_SYNC_CORRVAL) && (CorrelPreamble(nCurSymbolIdx) > G_dMaxSyncCorrel)
                G_dMaxSyncCorrel = CorrelPreamble(nCurSymbolIdx);
                G_dSyncSymbolIndex = nCurSymbolIdx;
                G_dMaxSyncCnt = 0;
            elseif (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL)
                G_dMaxSyncCnt = G_dMaxSyncCnt + 1;
            end
                
            if (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL) && (G_dMaxSyncCnt >= MAX_SYNC_COUNT)
                range = (G_dSyncSymbolIndex-DC_AVG_COUNT-DC_AVG_OFFSET+1:G_dSyncSymbolIndex-DC_AVG_OFFSET);
                if (ENABLE_GMSK_RX_FLT > 0)
                    dc_sum = sum(G_pFilteredData(range));
                else
                    dc_sum = sum(G_pSrcDataCh1(range));
                end
                G_wRxReferValue = dc_sum / length(range) + DC_GAP;

                if (nCurSymbolIdx > G_PreOffset)
                    G_PreStart = nCurSymbolIdx-G_PreOffset;
                else
                    G_PreStart = 1;
                end

                %--------------------------------------------
                if (ENABLE_PLOT1 == 1)
                    h_fig1 = figure(1);
                    h_fig1.Name = 'Detected Preamble Data(Matched Filter)';
                    x1 = G_PreStart:nCurSymbolIdx;
                    plot(x1, G_pSrcDataCh1(x1), '-x', x1, G_pFilteredData(x1), '-o', x1, CorrelPreamble(x1), '-+'); grid; 
                    title('Detected\_Preamble_Data'); yline(G_wRxReferValue,'-m',G_wRxReferValue,'LineWidth',2);
                    xline(G_dSyncSymbolIndex,'--', 'Max picked correlation');
                    xline(G_dSyncSymbolIndex-DC_AVG_COUNT-DC_AVG_OFFSET+1,'--', 'DC Offset sum start');
                    xline(G_dSyncSymbolIndex-DC_AVG_OFFSET,'--', 'DC Offset sum end');
                end
                %--------------------------------------------

                %index_max = find(impulse_response_of_gmsk == max(impulse_response_of_gmsk));
                %half_impulse_response_os = impulse_response_of_gmsk(index_max:(index_max+2*OSR));
                %calc_coefficient(G_pFilteredData(nCurSymbolIdx - length(G_vGmskPreamble) + 1 : nCurSymbolIdx), G_vGmskPreamble', half_impulse_response_os);

                %% 
                G_dStartSymbolIndex = nCurSymbolIdx - START_DETECT_OFFSET;
                G_dMaxSyncCorrel= 0;
                G_dMaxSyncCnt   = 0;

                G_wRxRunStatus  = RX_MDM_STATUS_START;
                G_wRxShiftReg   = 0;
                G_wRxBitCount   = 0;
                G_wRxPrevBitD   = G_wRxNrziPrev;
                G_wBitSamplCntr = 1;
                G_dRxAdcErrCnt  = 0;

                G_dSwRxPllValue = RX_PLL_HALF;
                G_dSwRxPllCntrX = 1;
                G_dSwRxPllSampC = G_wRxNrziPrev;
                G_dSwRxPllSampP = G_wRxNrziPrev;

                G_xPreData.nPntX = 0;
                G_xPreData.dSumX = 0;
                G_xPreData.dCntX = 0;
                G_xPreData.wAvrX = DC_MID_LEVEL;

                G_xDotData.wDotPattern = 0;
                G_xDotData.wDotChanged = 0;
                G_xDotData.wDotCountX = 0;

                G_dRxAfAdcSumVal = 0;
                G_dRxAfAdcCntVal = 0;

                G_dSyncDetCnt = G_dSyncDetCnt + 1;
            end
        end
    end

    AdativeDcOffset(nCurSymbolIdx) = G_wRxReferValue;

    % Check Max/Min Lever voltage range
    if (    (ENABLE_ADC_LIMIT == 1) && ((G_pSrcDataCh1(nCurSymbolIdx) < DC_MIN_LEVEL) || (G_pSrcDataCh1(nCurSymbolIdx) > DC_MAX_LEVEL))) ...
        || ((ENABLE_ADC_LIMIT == 2) && (G_pSrcDataCh1(nCurSymbolIdx) > DC_MAX_LEVEL))
    %if (    (ENABLE_ADC_LIMIT == 1) && (G_pFilteredData(nCurSymbolIdx) < DC_MIN_LEVEL || G_pFilteredData(nCurSymbolIdx) > DC_MAX_LEVEL)) ...
    %    || ((ENABLE_ADC_LIMIT == 2) && (G_pFilteredData(nCurSymbolIdx) > DC_MAX_LEVEL))
        G_xPreData.nPntX = 0;
        G_xPreData.dSumX = 0;
        G_xPreData.dCntX = 0;
        G_xPreData.wAvrX = DC_MID_LEVEL;

        if(G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        %if(G_wRxRunStatus <= RX_MDM_STATUS_START)
            G_dRxAdcErrCnt = 0;
            % Init preamble correlation value and count
            % AD Error의 경우 correlation max value and count를 초기화 해주면 수신 성능이
            % 좋아짐.
            G_dMaxSyncCorrel= 0;
            G_dMaxSyncCnt   = 0;
        else
            G_dRxAdcErrCnt = G_dRxAdcErrCnt + 1;
            if(G_dRxAdcErrCnt > ADC_MAX_ERROR_CNT)
                G_dRxAdcErrCnt = 0;

                %--------------------------------------------
                if (ENABLE_PLOT96 == 1)
                    h_fig96 = figure(96);
                    h_fig96.Name = 'Adc Max/Min Error';
                    x1 = G_PreStart:nCurSymbolIdx-SYNC_DETECT_OFFSET+50;
                    x2 = G_PreStart:nCurSymbolIdx-SYNC_DETECT_OFFSET;
                    plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m'); grid; 
                    title('Error Max/Min Vol.');
                end
                %--------------------------------------------

                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                G_wRxShiftReg  = 0;
                G_wRxReferValue= DC_MID_LEVEL;

                G_xDotData.wDotPattern = 0;
                G_xDotData.wDotChanged = 0;
                G_xDotData.wDotCountX = 0;
                G_dAdcErrCnt = G_dAdcErrCnt+1;
            end
        end
    end

    if (G_wRxRunStatus ~= RX_MDM_STATUS_PREAMBLE)
        for idx = (G_dStartSymbolIndex : nCurSymbolIdx)
            if (ENABLE_GMSK_RX_FLT > 0)
                G_wRxAfAdcData = G_pFilteredData(idx);
            else
                G_wRxAfAdcData = G_pSrcDataCh1(idx);
            end

            % ★ Joint MLSD + Timing Recovery 구현 (PLL 완전 대체)
            if ENABLE_JOINT_MLSD_TIMING == 1
                % 현재 신호 샘플 추출
                if (ENABLE_GMSK_RX_FLT > 0)
                    current_signal = G_pFilteredData(idx);
                else
                    current_signal = G_pSrcDataCh1(idx);
                end

                % Joint MLSD + Timing Recovery 실행 (안전한 호출)
                if idx > MLSD_TIMING_WINDOW && idx <= length(G_pSrcDataCh1) - MLSD_TIMING_WINDOW
                    [G_wRxCurrBitD, timing_decision, mlsd_confidence] = joint_mlsd_timing_recovery(current_signal, idx);
                else
                    % 경계 조건에서는 기본 NRZI 디코딩 사용
                    G_wRxCurrBitD = nrzi_decode(G_wRxAfAdcData, G_wRxReferValue);
                    timing_decision = 1;  % 강제로 심볼 경계로 설정
                    mlsd_confidence = 0.1;
                end

                % 타이밍 결정에 따른 카운터 업데이트
                if timing_decision == 1  % 심볼 경계 검출
                    G_wRxNrziCntr = 1;
                    G_dSwRxPllCntrX = 1;
                    G_MLSDSymbolCounter = G_MLSDSymbolCounter + 1;

                    % PLL 호환성을 위한 변수 업데이트
                    G_dSwRxPllSampC = G_wRxCurrBitD;
                    G_dSwRxPllSampP = G_dSwRxPllSampC;
                    G_dSwRxPllValue = 0;  % 심볼 경계에서 리셋
                else
                    % 심볼 내부 샘플
                    G_wRxNrziCntr = G_wRxNrziCntr + 1;
                    G_dSwRxPllCntrX = G_dSwRxPllCntrX + 1;
                    G_dSwRxPllValue = G_dSwRxPllValue + RX_PLL_INCR;

                    % 다음 샘플로 계속
                    if G_wRxNrziCntr < OSR
                        continue;
                    end
                end

                % 통계 업데이트
                G_ViterbiStats.total_decisions = G_ViterbiStats.total_decisions + 1;
                if mlsd_confidence > VITERBI_CONFIDENCE_THRESHOLD
                    G_ViterbiStats.viterbi_decisions = G_ViterbiStats.viterbi_decisions + 1;
                end
                G_ViterbiStats.confidence_sum = G_ViterbiStats.confidence_sum + mlsd_confidence;

            else
                % ★ 기존 PLL 로직 (백업용)
                G_wRxNrziCurr = nrzi_decode(G_wRxAfAdcData, G_wRxReferValue);
                G_dSwRxPllSampC = G_wRxNrziCurr;

                if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
                    if((G_wRxNrziCntr <= (OSR - 2)) || ((G_wRxNrziCntr == (OSR - 1)) && (G_dSwRxPllValue >= (RX_PLL_FULL - RX_PLL_INCR + RX_PLL_STEP))))
                        G_wRxNrziCntr = G_wRxNrziCntr + 1;
                        G_dSwRxPllSampC = G_dSwRxPllSampP;
                    else
                        G_wRxNrziCntr = 1;
                    end
                else
                    G_wRxNrziCntr = G_wRxNrziCntr + 1;
                end

                if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
                    if (G_wRxRunStatus == RX_MDM_STATUS_START)
                        if (G_dSwRxPllCntrX >= (OSR * 2 - 3)) && (G_dSwRxPllCntrX <= (OSR * 2 + 3))
                            G_dSwRxPllValue = RX_PLL_HALF;
                        end
                    end

                    if (G_dSwRxPllValue < RX_PLL_HALF)
                        G_dSwRxPllValue = (G_dSwRxPllValue + RX_PLL_STEP);
                    else
                        G_dSwRxPllValue = (G_dSwRxPllValue - RX_PLL_STEP);
                    end

                    G_dSwRxPllCntrX = 1;
                else
                    G_dSwRxPllCntrX = G_dSwRxPllCntrX + 1;
                end

                G_dSwRxPllSampP = G_dSwRxPllSampC;

                G_dSwRxPllValue = G_dSwRxPllValue + RX_PLL_INCR;
                if(G_dSwRxPllValue >= RX_PLL_FULL)
                    G_dSwRxPllValue = G_dSwRxPllValue - RX_PLL_FULL;
                else
                    continue;
                end

                G_wRxCurrBitD = G_dSwRxPllSampC;
            end

            % Joint MLSD + Timing Recovery에서 이미 G_wRxCurrBitD가 설정됨
            % 추가 처리 없음

            %%%%% ProcessRxDataCommonRun()
            G_wRxShiftReg = bitshift(G_wRxShiftReg, 1);
            if (G_wRxCurrBitD == G_wRxPrevBitD)
                G_wRxShiftReg = bitor(G_wRxShiftReg, 0x0001);
                BitArray(idx) = 0.05;
            else
                G_wRxShiftReg = bitand(G_wRxShiftReg, 0xfffe);
                BitArray(idx) = 0.015;

                if (ENABLE_ADAPT_DC_OFFSET == 1)
                    % 개선된 적응형 DC Offset 추적
                    % 신호 품질에 따른 적응형 알파 값
                    if G_SignalQuality > 2.5
                        alpha_dc = 0.900;  % 신호가 좋으면 더 빠른 적응
                    elseif G_SignalQuality > 1.5
                        alpha_dc = 0.850;  % 기본값
                    else
                        alpha_dc = 0.800;  % 신호가 나쁘면 더 느린 적응
                    end

                    % 반전 구간에서만 DC Offset 보정
                    if (ENABLE_GMSK_RX_FLT > 0)
                        sample = G_pFilteredData(idx);
                    else
                        sample = G_pSrcDataCh1(idx);
                    end
                    G_wRxReferValue = update_dc_offset_on_invert(G_wRxReferValue, sample, alpha_dc);
                end
            end

            G_wRxPrevBitD = G_wRxCurrBitD;

            switch (G_wRxRunStatus)
                case RX_MDM_STATUS_START
                    % 개선된 시작 비트 검출 (더 유연한 패턴 매칭)
                    start_pattern_detected = false;

                    % 다양한 시작 패턴 검사 (노이즈에 더 강건)
                    if (bitand(G_wRxShiftReg, 0x003f) == 0x003e || ...
                        bitand(G_wRxShiftReg, 0x001f) == 0x001e || ...
                        bitand(G_wRxShiftReg, 0x007f) == 0x007e)
                        start_pattern_detected = true;
                    end

                    if start_pattern_detected
                        %%----------------------------------------------------------
                        if (ENABLE_PLOT2 == 1)
                            h_fig2 = figure(2);
                            h_fig2.Name = 'Detected Start Data';
                            x1 = G_PreStart:idx;
                            x2 = G_PreStart:idx;
                            subplot(2,1,1); plot(x1, G_pSrcDataCh1(x1), '-x', x1, G_pFilteredData(x1), '-o', x1, AdativeDcOffset(x1), '-m'); grid; 
                                            title('detected\_start\_data');
                            subplot(2,1,2); plot(x2, G_pSrcDataCh1(x2), '-x', x1, G_pFilteredData(x1), '-o'); grid; 
                                            title('filtered\_start\_data');
                        end
                        %----------------------------------------------------------

                        G_wRxBitCount    = 0;
                        G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                    else
                        G_wRxBitCount = G_wRxBitCount + 1;
                        if(G_wRxBitCount >= 25)
                            %----------------------------------------------------------
                            if (ENABLE_PLOT97 == 1)
                                h_fig97 = figure(97);
                                h_fig97.Name = 'Start Bit Error';
                                x1 = G_PreStart:idx+100;
                                x2 = G_PreStart:idx;
                                plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m', x2, BitArray(x2), '-+'); grid; 
                                xline(idx,'--', 'Current Positon');
                                title('Error Start Bit');
                            end
                            %----------------------------------------------------------

                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_dStartErrCnt = G_dStartErrCnt + 1;
                        end
                    end

                case RX_MDM_STATUS_PRELOAD
                    G_wRxBitCount = G_wRxBitCount + 1;
                    if (G_wRxBitCount == 8)
                        G_wRxBitCount = 0;
                        G_wCrcRegData = 0xffff;
                        G_wRxRunStatus= RX_MDM_STATUS_DATA;
                        %ClrRxRawFormTemp();

                        nTemp = bitshift(G_wRxShiftReg, 2);
                        nTemp = bitand(nTemp, 0x00ff);
                        nMsgID = G_vReverDataTableX(nTemp + 1);
                        if (nMsgID < 0) || (nMsgID > 27)
                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_dPloadErrCnt = G_dPloadErrCnt + 1;
                        else
                            m_wRxMaxBitSize = (G_vMaxBitSize(nMsgID + 1) + 16 + 2);
                        end
                    end

                case RX_MDM_STATUS_DATA
                    if (bitand(G_wRxShiftReg, 0x3f00) ~= 0x3e00)      % It's not a stuffing bit
                        G_wRxBitCount = G_wRxBitCount + 1;
                        if(G_wRxBitCount >= m_wRxMaxBitSize+1)
                            %----------------------------------------------------------
                            if (ENABLE_PLOT98 == 1)
                                h_fig98 = figure(98);
                                h_fig98.Name = 'Stuffing Bit Error';
                                x1 = G_PreStart:idx;
                                x2 = G_PreStart:idx;
                                plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m', x2, BitArray(x2), '-+'); grid; 
                                title('Error Stuffing Bit');
                            end
                            %----------------------------------------------------------

                            %%%%%% ResetToRxStatusPreamble()
                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_bRxByteData = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_dStuffErrCnt = G_dStuffErrCnt + 1;
                            continue;
                        end

                        G_wNewBitData = bitand(bitshift(G_wRxShiftReg, -8), 0x0001);
                        G_bRxByteData = bitor(bitshift(G_bRxByteData, -1), bitand(bitshift(G_wRxShiftReg, -1), 0x0080));
                        G_BitDataArray(G_wRxBitCount) = G_wNewBitData;

                        G_wCrcRegData = update_crc(G_wCrcRegData, G_wNewBitData);
                    end

                    if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                    %if (bitand(G_wRxShiftReg, 0x007f) == 0x007e)
                        if(G_wCrcRegData == 0xf0b8)                                 % This should give a result of 0xF0B8
                            %----------------------------------------------------------
                            if (ENABLE_PLOT3 == 1)
                                h_fig3 = figure(3);
                                h_fig3.Name = 'Received Data(CRC OK)';
                                x1 = G_PreStart:idx+50;
                                if (G_PreStart <= SYNC_DETECT_OFFSET)
                                    x2 = G_PreStart:nCurSymbolIdx;
                                    x3 = G_PreStart:nCurSymbolIdx;
                                else
                                    x2 = G_PreStart:idx;
                                    x3 = G_PreStart+SYNC_DETECT_OFFSET:nCurSymbolIdx;
                                end
                                plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x3, AdativeDcOffset(x3), '-m', x2, BitArray(x2), '-+'); grid; 
                                title('Received AIS Packet');
                            end
                            %----------------------------------------------------------

                            if (G_PreStart > (215042-10)) && (G_PreStart < (215042+10))
                                break;
                            end

                            %WritePacketIntoRxRawBuff();
                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_bRxByteData = 0;
                            G_dRcvPktCnt = G_dRcvPktCnt + 1;
                        else
                            %----------------------------------------------------------
                            if (ENABLE_PLOT99 == 1)
                                h_fig99 = figure(99);
                                h_fig99.Name = 'Received Data(CRC ERROR)';
                                x1 = G_PreStart:idx;
                                if (G_PreStart <= SYNC_DETECT_OFFSET)
                                    x2 = G_PreStart:nCurSymbolIdx;
                                    x3 = G_PreStart:nCurSymbolIdx;
                                else
                                    x2 = G_PreStart:idx;
                                    x3 = G_PreStart+SYNC_DETECT_OFFSET:nCurSymbolIdx;
                                end
                                plot(x2, G_pFilteredData(x2), '-o', x3, AdativeDcOffset(x3), '-m', x2, BitArray(x2), '-+'); grid; 
                                title('Received Error AIS Packet');
                            end
                            %----------------------------------------------------------

                            %if (G_PreStart > (426187-10) && G_PreStart < (426187+10))
                            %    break;
                            %end

                            %m_dSampleCounter = cAisModem::GetSampleCounterValue();
                            %m_dSlotNoCounter = cAisModem::GetSlotNoCounterValue();

                            G_wRxBitCount    = 0;
                            %G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                            G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
                            G_bRxByteData    = 0;
                            G_dCrcErrCnt     = G_dCrcErrCnt + 1;
                            G_dCRCErrSymIdx(G_dCrcErrCnt) = G_PreStart;
                        end
                    end

                otherwise
                    warning('Unexpected run status.');
            end
        end
        G_dStartSymbolIndex = nCurSymbolIdx+1;
    end
end

%-------------------------------------------------------------------------
figure(9);
bar_x = ["SyncDet" "AdcErr" "StartErr" "StuffErr" "CrcErr" "Packet OK" ];
bar_y = [G_dSyncDetCnt, G_dAdcErrCnt G_dStartErrCnt G_dStuffErrCnt G_dCrcErrCnt G_dRcvPktCnt];
b = bar(bar_x, bar_y, 'FaceColor', 'flat');
b.CData(6,:) = [0.6350 0.0780 0.1840];
xtips1 = b(1).XEndPoints;
ytips1 = b(1).YEndPoints;
labels1 = string(b(1).YData);
text(xtips1,ytips1,labels1,'HorizontalAlignment','center','VerticalAlignment','bottom')

% ★ Joint MLSD + Timing Recovery 성능 출력 (추가)
if ENABLE_JOINT_MLSD_TIMING == 1
    fprintf('\n=== Joint MLSD + Timing Recovery 성능 ===\n');
    fprintf('총 비트 결정: %d개\n', G_ViterbiStats.total_decisions);
    fprintf('MLSD 고신뢰도 결정: %d개\n', G_ViterbiStats.viterbi_decisions);
    fprintf('MLSD 심볼 카운터: %d개\n', G_MLSDSymbolCounter);
    fprintf('타이밍 오프셋: %.3f\n', G_MLSDTimingOffset);
    fprintf('타이밍 품질: %.3f\n', G_MLSDTimingQuality);
    if G_MLSDTimingLocked
        fprintf('타이밍 락 상태: LOCKED\n');
    else
        fprintf('타이밍 락 상태: UNLOCKED\n');
    end

    if G_ViterbiStats.total_decisions > 0
        avg_confidence = G_ViterbiStats.confidence_sum / G_ViterbiStats.total_decisions;
        mlsd_ratio = (G_ViterbiStats.viterbi_decisions / G_ViterbiStats.total_decisions) * 100;
        fprintf('평균 신뢰도: %.3f\n', avg_confidence);
        fprintf('MLSD 활용률: %.1f%%\n', mlsd_ratio);
    end

    % 그래프 제목 업데이트
    title('AIS Receiver with Joint MLSD + Timing Recovery');
elseif ENABLE_VITERBI_MLSD == 1
    fprintf('\n=== Viterbi MLSD 성능 ===\n');
    fprintf('총 비트 결정: %d개\n', G_ViterbiStats.total_decisions);
    fprintf('Viterbi 고신뢰도 결정: %d개\n', G_ViterbiStats.viterbi_decisions);
    if G_ViterbiStats.total_decisions > 0
        avg_confidence = G_ViterbiStats.confidence_sum / G_ViterbiStats.total_decisions;
        viterbi_ratio = (G_ViterbiStats.viterbi_decisions / G_ViterbiStats.total_decisions) * 100;
        fprintf('평균 신뢰도: %.3f\n', avg_confidence);
        fprintf('Viterbi 활용률: %.1f%%\n', viterbi_ratio);
    end
    title('AIS Receiver with Viterbi MLSD');
else
    title('AIS Receiver (Original)');
end

%-------------------------------------------------------------------------

function [h0, bias] = calc_coefficient(received_data, source_data, half_impulse_response)
    INDEX_START = 1;

    %received_data = received_data - 0.1;
    A = [source_data(INDEX_START:length(received_data))', ones(length(received_data),1)];

    pinv_data = pinv(A);
    received_data = received_data';
    coeff_vector = pinv_data*received_data;
    h0 = coeff_vector(1)*max(half_impulse_response);
    bias = coeff_vector(2);

    figure(34)
    kkk = 1:length(received_data);
    scale = h0/max(half_impulse_response);
    subplot(4,1,1); plot(received_data); grid; title('received\_data');
    subplot(4,1,2); plot(source_data(INDEX_START:INDEX_START+length(received_data)-1)); grid; title('source\_data');
    subplot(4,1,3); plot(kkk, scale*source_data(INDEX_START:INDEX_START+length(received_data)-1), '-o', kkk, scale*source_data(INDEX_START:INDEX_START+length(received_data)-1)+bias, '-x', kkk, received_data, '-+'); grid; title('source\_data (o), received\_data (x)');
    subplot(4,1,4); plot(kkk, pinv_data(1,:), '-o', kkk, pinv_data(2,:), '-x'); grid; title('test');
end

%------------------------------------------------------------------------
% ★ 개선된 Viterbi MLSD 비트 결정 함수 (핵심)
%------------------------------------------------------------------------
function [bit_decision, confidence] = enhanced_viterbi_mlsd_decision(signal_sample, pll_sample, current_idx)
    global G_ViterbiBuffer G_ViterbiIndex VITERBI_WINDOW_SIZE;
    global G_pFilteredData ENABLE_GMSK_RX_FLT G_pSrcDataCh1;

    % 기본값 설정
    bit_decision = pll_sample;
    confidence = 0.3;

    % Viterbi 버퍼에 현재 신호 샘플 추가
    G_ViterbiBuffer(G_ViterbiIndex) = signal_sample;
    G_ViterbiIndex = mod(G_ViterbiIndex, VITERBI_WINDOW_SIZE) + 1;

    % 충분한 히스토리가 있을 때만 MLSD 적용
    if current_idx < VITERBI_WINDOW_SIZE
        return;
    end

    % 신호 윈도우 추출 (오버샘플링 고려)
    window_size = VITERBI_WINDOW_SIZE * 3;  % 더 큰 윈도우
    signal_start = max(1, current_idx - window_size);
    signal_end = min(length(G_pFilteredData), current_idx + window_size);

    if signal_end <= signal_start + 10
        return;
    end

    % 신호 데이터 선택
    if (ENABLE_GMSK_RX_FLT > 0)
        signal_window = G_pFilteredData(signal_start:signal_end);
    else
        signal_window = G_pSrcDataCh1(signal_start:signal_end);
    end

    % 개선된 Viterbi MLSD 알고리즘 적용
    [mlsd_bit, mlsd_confidence] = advanced_viterbi_mlsd(signal_window, G_ViterbiBuffer, current_idx - signal_start + 1);

    % 신뢰도 기반 결정
    if mlsd_confidence > 0.4
        bit_decision = mlsd_bit;
        confidence = mlsd_confidence;
    elseif mlsd_confidence > 0.25
        % 중간 신뢰도: PLL과 MLSD 결합
        if mlsd_bit == pll_sample
            bit_decision = mlsd_bit;
            confidence = mlsd_confidence + 0.1;
        else
            % 불일치 시 더 신중한 결정
            bit_decision = pll_sample;
            confidence = 0.2;
        end
    end
end

%------------------------------------------------------------------------
% 간단한 Viterbi MLSD 구현
%------------------------------------------------------------------------
function [bit_decision, confidence] = simple_viterbi_mlsd(signal_window, bit_history)
    % 기본값
    if ~isempty(bit_history)
        bit_decision = bit_history(length(bit_history));
    else
        bit_decision = 0;
    end
    confidence = 0.4;

    if (length(signal_window) < 10) || (length(bit_history) < 4)
        return;
    end

    % 신호 정규화
    signal_mean = mean(signal_window);
    signal_std = std(signal_window);
    if signal_std > 0.01
        normalized_signal = (signal_window - signal_mean) / signal_std;
    else
        normalized_signal = signal_window - signal_mean;
    end

    % 2-상태 Viterbi (간단한 버전)
    num_states = 2;
    window_length = min(length(normalized_signal), 15);  % 최대 15 샘플

    % 경로 메트릭 초기화
    path_metrics = [0, inf];

    % Viterbi 알고리즘
    for t = 1:window_length
        new_metrics = [inf, inf];
        current_sample = normalized_signal(t);

        for prev_state = 1:num_states
            if path_metrics(prev_state) < inf
                for output_bit = 0:1
                    % 예상 신호 레벨
                    if output_bit == 0
                        expected_level = -0.4;
                        next_state = 1;
                    else
                        expected_level = 0.4;
                        next_state = 2;
                    end

                    % 브랜치 메트릭
                    error = current_sample - expected_level;
                    branch_metric = error^2;
                    total_metric = path_metrics(prev_state) + branch_metric;

                    if total_metric < new_metrics(next_state)
                        new_metrics(next_state) = total_metric;
                        if t == window_length
                            bit_decision = output_bit;
                        end
                    end
                end
            end
        end

        path_metrics = new_metrics;
    end

    % 신뢰도 계산
    [min_metric, ~] = min(path_metrics);
    if min_metric < inf
        confidence = exp(-min_metric / window_length);
        confidence = max(0.2, min(0.9, confidence));
    end

    % 비트 히스토리와의 일관성 체크
    if length(bit_history) >= 3
        hist_len = length(bit_history);
        recent_pattern = bit_history(hist_len-2:hist_len);
        if sum(recent_pattern == bit_decision) >= 2
            confidence = confidence * 1.1;  % 일관성 보너스
        end
    end

    confidence = min(0.9, confidence);
end

%------------------------------------------------------------------------
% ★ 고급 Viterbi MLSD 구현 (개선된 알고리즘)
%------------------------------------------------------------------------
function [bit_decision, confidence] = advanced_viterbi_mlsd(signal_window, bit_history, center_idx)
    % 기본값 설정
    bit_decision = 0;
    confidence = 0.3;

    if (length(signal_window) < 15) || (center_idx < 5) || (center_idx > length(signal_window) - 5)
        return;
    end

    % 신호 전처리 및 정규화
    signal_mean = mean(signal_window);
    signal_std = std(signal_window);
    if signal_std < 0.01
        signal_std = 0.01;  % 최소값 설정
    end
    normalized_signal = (signal_window - signal_mean) / signal_std;

    % GMSK 채널 모델 파라미터 (AIS 특화)
    h0 = 0.7;   % 현재 비트 영향
    h1 = 0.3;   % 이전 비트 영향

    % 4-상태 Viterbi MLSD (더 정확한 모델)
    % 상태: [이전비트, 현재비트] = [00, 01, 10, 11]
    num_states = 4;
    traceback_length = min(12, length(normalized_signal) - 2);

    % 경로 메트릭 및 생존 경로 초기화
    path_metrics = [0, inf, inf, inf];
    survivor_paths = zeros(num_states, traceback_length);

    % 중심점 주변 처리
    start_idx = max(1, center_idx - 6);
    end_idx = min(length(normalized_signal), center_idx + 6);
    process_length = end_idx - start_idx + 1;

    if process_length < 8
        return;
    end

    % Viterbi 알고리즘 실행
    for t = 1:process_length
        new_metrics = inf(1, num_states);
        new_paths = zeros(num_states, traceback_length);
        current_sample = normalized_signal(start_idx + t - 1);

        for current_bit = 0:1
            for prev_bit = 0:1
                % 현재 상태 계산
                current_state = prev_bit * 2 + current_bit + 1;

                % 이전 상태들 확인
                for prev_prev_bit = 0:1
                    prev_state = prev_prev_bit * 2 + prev_bit + 1;

                    if path_metrics(prev_state) < inf
                        % GMSK 신호 예측 (ISI 고려)
                        if t > 1
                            expected_signal = h0 * (2*current_bit - 1) + h1 * (2*prev_bit - 1);
                        else
                            expected_signal = h0 * (2*current_bit - 1);
                        end

                        % 브랜치 메트릭 계산 (유클리드 거리)
                        error = current_sample - expected_signal;
                        branch_metric = error^2;

                        % 총 메트릭 계산
                        total_metric = path_metrics(prev_state) + branch_metric;

                        % 생존 경로 업데이트
                        if total_metric < new_metrics(current_state)
                            new_metrics(current_state) = total_metric;

                            % 경로 히스토리 업데이트
                            if t > 1
                                new_paths(current_state, 1:end-1) = survivor_paths(prev_state, 2:end);
                            end
                            new_paths(current_state, end) = current_bit;
                        end
                    end
                end
            end
        end

        % 메트릭 및 경로 업데이트
        path_metrics = new_metrics;
        survivor_paths = new_paths;
    end

    % 최적 경로 선택 및 비트 결정
    [min_metric, best_state] = min(path_metrics);

    if min_metric < inf
        % 중심점에 해당하는 비트 추출
        center_bit_idx = ceil(traceback_length / 2);
        if center_bit_idx <= size(survivor_paths, 2)
            bit_decision = survivor_paths(best_state, center_bit_idx);
        else
            bit_decision = survivor_paths(best_state, end);
        end

        % 신뢰도 계산 (메트릭 차이 기반)
        sorted_metrics = sort(path_metrics);
        if (length(sorted_metrics) >= 2) && (sorted_metrics(2) < inf)
            metric_diff = sorted_metrics(2) - sorted_metrics(1);
            confidence = min(0.95, max(0.1, metric_diff / (1 + sorted_metrics(1))));
        else
            confidence = 0.8;  % 단일 생존 경로
        end

        % 신호 품질 기반 신뢰도 조정
        signal_quality = 1 / (1 + signal_std);
        confidence = confidence * signal_quality;

        % 비트 히스토리와의 일관성 체크 (추가 신뢰도)
        if (~isempty(bit_history)) && (length(bit_history) >= 3)
            recent_bits = bit_history(max(1, end-2):end);
            if sum(recent_bits == bit_decision) >= 2
                confidence = min(0.95, confidence * 1.1);
            end
        end
    end
end

%------------------------------------------------------------------------
% ★ Joint MLSD + Timing Recovery 함수 (PLL 완전 대체)
%------------------------------------------------------------------------
function [bit_decision, timing_decision, confidence] = joint_mlsd_timing_recovery(signal_sample, current_idx)
    global G_MLSDTimingBuffer G_MLSDTimingIndex MLSD_TIMING_WINDOW;
    global G_MLSDSymbolBuffer G_MLSDSymbolIndex MLSD_SYMBOL_BUFFER_SIZE;
    global G_MLSDTimingOffset G_MLSDTimingPhase G_MLSDSampleCounter;
    global G_MLSDTimingLocked G_MLSDTimingQuality;
    global G_pFilteredData ENABLE_GMSK_RX_FLT G_pSrcDataCh1;

    % 기본값 설정
    bit_decision = 0;
    timing_decision = 0;  % 0: 심볼 내부, 1: 심볼 경계
    confidence = 0.3;

    % 타이밍 버퍼에 현재 샘플 추가
    G_MLSDTimingBuffer(G_MLSDTimingIndex) = signal_sample;
    G_MLSDTimingIndex = mod(G_MLSDTimingIndex, MLSD_TIMING_WINDOW) + 1;
    G_MLSDSampleCounter = G_MLSDSampleCounter + 1;

    % 충분한 샘플이 있을 때만 처리
    if (current_idx < MLSD_TIMING_WINDOW) || (G_MLSDSampleCounter < 10)
        return;
    end

    % 신호 윈도우 추출 (안전한 인덱스 확인)
    if (ENABLE_GMSK_RX_FLT > 0)
        data_length = length(G_pFilteredData);
        signal_data = G_pFilteredData;
    else
        data_length = length(G_pSrcDataCh1);
        signal_data = G_pSrcDataCh1;
    end

    window_start = max(1, current_idx - MLSD_TIMING_WINDOW);
    window_end = min(data_length, current_idx + MLSD_TIMING_WINDOW);

    if (window_end <= window_start + 20) || (current_idx > data_length)
        return;
    end

    signal_window = signal_data(window_start:window_end);

    % Joint MLSD + Timing 알고리즘 실행 (안전한 호출)
    try
        [bit_decision, timing_offset, confidence] = joint_mlsd_algorithm(signal_window, G_MLSDTimingBuffer);
    catch
        % 오류 발생 시 기본값 사용
        bit_decision = 0;
        timing_offset = 0;
        confidence = 0.1;
    end

    % 타이밍 오프셋 업데이트
    G_MLSDTimingOffset = 0.8 * G_MLSDTimingOffset + 0.2 * timing_offset;

    % 타이밍 위상 업데이트 (OSR = 5 고려)
    G_MLSDTimingPhase = G_MLSDTimingPhase + 1;

    % 심볼 경계 결정 (OSR = 5 기준)
    timing_phase_adjusted = G_MLSDTimingPhase + round(G_MLSDTimingOffset);
    if mod(timing_phase_adjusted, 5) == 0
        timing_decision = 1;  % 심볼 경계
        G_MLSDTimingPhase = 0.0;  % 위상 리셋

        % 심볼 버퍼에 비트 추가
        G_MLSDSymbolBuffer(G_MLSDSymbolIndex) = bit_decision;
        G_MLSDSymbolIndex = mod(G_MLSDSymbolIndex, MLSD_SYMBOL_BUFFER_SIZE) + 1;

        % 타이밍 품질 업데이트
        G_MLSDTimingQuality = 0.9 * G_MLSDTimingQuality + 0.1 * confidence;

        % 타이밍 락 상태 업데이트
        if G_MLSDTimingQuality > 0.6
            G_MLSDTimingLocked = true;
        elseif G_MLSDTimingQuality < 0.3
            G_MLSDTimingLocked = false;
        end
    end
end

%------------------------------------------------------------------------
% ★ Joint MLSD 알고리즘 (비트 결정 + 타이밍 추정)
%------------------------------------------------------------------------
function [bit_decision, timing_offset, confidence] = joint_mlsd_algorithm(signal_window, timing_buffer)
    % 기본값 설정
    bit_decision = 0;
    timing_offset = 0;
    confidence = 0.3;

    % 입력 검증
    if isempty(signal_window) || isempty(timing_buffer) || ...
       (length(signal_window) < 25) || (length(timing_buffer) < 8)
        return;
    end

    % 신호 전처리
    signal_mean = mean(signal_window);
    signal_std = std(signal_window);
    if signal_std < 0.01
        signal_std = 0.01;
    end
    normalized_signal = (signal_window - signal_mean) / signal_std;

    % GMSK 채널 모델 파라미터
    h0 = 0.8;   % 현재 비트 영향
    h1 = 0.4;   % 이전 비트 영향 (ISI)

    % 타이밍 오프셋 후보들 (-2 ~ +2 샘플)
    timing_candidates = -2:0.5:2;
    best_metric = inf;
    best_timing = 0;
    best_bit = 0;

    % 각 타이밍 후보에 대해 MLSD 실행
    for timing_idx = 1:length(timing_candidates)
        current_timing = timing_candidates(timing_idx);

        % 타이밍 오프셋을 적용한 샘플링 포인트 계산
        center_idx = ceil(length(normalized_signal) / 2);
        sample_points = center_idx + current_timing + (-10:5:10);  % OSR=5 고려

        % 유효한 샘플 포인트만 선택 (안전한 인덱스 확인)
        valid_mask = (sample_points >= 1) & (sample_points <= length(normalized_signal));
        valid_points = sample_points(valid_mask);
        valid_points = round(valid_points);  % 정수 인덱스로 변환

        if length(valid_points) < 3
            continue;
        end

        % 해당 타이밍에서 4-상태 Viterbi MLSD 실행
        [candidate_bit, mlsd_metric] = timing_aware_viterbi_mlsd(normalized_signal, valid_points, h0, h1);

        % 최적 타이밍 선택
        if mlsd_metric < best_metric
            best_metric = mlsd_metric;
            best_timing = current_timing;
            best_bit = candidate_bit;
        end
    end

    % 결과 설정
    bit_decision = best_bit;
    timing_offset = best_timing;

    % 신뢰도 계산
    if best_metric < inf
        confidence = exp(-best_metric / 5);  % 메트릭 기반 신뢰도
        confidence = max(0.1, min(0.95, confidence));

        % 신호 품질 기반 조정
        signal_quality = 1 / (1 + signal_std);
        confidence = confidence * signal_quality;

        % 타이밍 히스토리와의 일관성 체크
        if (~isempty(timing_buffer)) && (length(timing_buffer) >= 5)
            recent_timing = timing_buffer(max(1, end-4):end);
            timing_consistency = 1 - std(recent_timing) / (abs(mean(recent_timing)) + 0.1);
            confidence = confidence * max(0.5, timing_consistency);
        end
    end
end

%------------------------------------------------------------------------
% ★ 타이밍 인식 Viterbi MLSD (특정 타이밍에서 비트 결정)
%------------------------------------------------------------------------
function [bit_decision, total_metric] = timing_aware_viterbi_mlsd(normalized_signal, sample_points, h0, h1)
    % 기본값
    bit_decision = 0;
    total_metric = inf;

    % 입력 검증
    if isempty(normalized_signal) || isempty(sample_points) || ...
       (length(sample_points) < 3) || (length(normalized_signal) < 10)
        return;
    end

    % 4-상태 Viterbi MLSD
    % 상태: [이전비트, 현재비트] = [00, 01, 10, 11]
    num_states = 4;
    num_samples = length(sample_points);

    % 경로 메트릭 초기화
    path_metrics = [0, inf, inf, inf];
    survivor_paths = zeros(num_states, num_samples);

    % Viterbi 알고리즘 실행
    for t = 1:num_samples
        new_metrics = inf(1, num_states);
        new_paths = zeros(num_states, num_samples);

        if t > length(sample_points)
            continue;
        end

        sample_idx = round(sample_points(t));
        if (sample_idx < 1) || (sample_idx > length(normalized_signal))
            continue;
        end

        current_sample = normalized_signal(sample_idx);

        for current_bit = 0:1
            for prev_bit = 0:1
                current_state = prev_bit * 2 + current_bit + 1;

                % 이전 상태들 확인
                for prev_prev_bit = 0:1
                    prev_state = prev_prev_bit * 2 + prev_bit + 1;

                    if path_metrics(prev_state) < inf
                        % GMSK 신호 예측 (ISI 고려)
                        if t > 1
                            expected_signal = h0 * (2*current_bit - 1) + h1 * (2*prev_bit - 1);
                        else
                            expected_signal = h0 * (2*current_bit - 1);
                        end

                        % 브랜치 메트릭 계산
                        error = current_sample - expected_signal;
                        branch_metric = error^2;

                        % 총 메트릭 계산
                        candidate_metric = path_metrics(prev_state) + branch_metric;

                        % 생존 경로 업데이트
                        if candidate_metric < new_metrics(current_state)
                            new_metrics(current_state) = candidate_metric;

                            % 경로 히스토리 업데이트
                            if t > 1
                                new_paths(current_state, 1:end-1) = survivor_paths(prev_state, 2:end);
                            end
                            new_paths(current_state, end) = current_bit;
                        end
                    end
                end
            end
        end

        % 메트릭 및 경로 업데이트
        path_metrics = new_metrics;
        survivor_paths = new_paths;
    end

    % 최적 경로 선택
    [total_metric, best_state] = min(path_metrics);

    if (total_metric < inf) && (best_state <= size(survivor_paths, 1))
        % 중심점 비트 추출
        center_idx = ceil(num_samples / 2);
        if center_idx <= size(survivor_paths, 2)
            bit_decision = survivor_paths(best_state, center_idx);
        else
            bit_decision = survivor_paths(best_state, end);
        end
    end
end